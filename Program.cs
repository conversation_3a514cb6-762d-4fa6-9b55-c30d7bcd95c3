using System;
using System.Linq;
using Sass.Pos.BusinessReport;

namespace KtvBusinessReportTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("KTV业务数据库探索分析程序");
            Console.WriteLine("==========================");
            Console.WriteLine();

            try
            {
                // 执行数据库探索分析
                var explorer = new DatabaseExplorer();
                explorer.RunCompleteAnalysis();

                Console.WriteLine("\n是否继续进行业务报告测试? (y/n)");
                var input = Console.ReadLine();

                if (input?.ToLower() == "y")
                {
                    // 业务报告生成测试
                    Console.WriteLine("\n正在测试业务报告生成...");
                    var reportSystem = new KtvBusinessReportSystem();

                    // 使用示例数据进行测试
                    int testShopId = 1; // 假设店铺ID为1
                    DateTime testDate = DateTime.Today; // 今天的业务日期

                    Console.WriteLine($"生成店铺ID {testShopId} 在 {testDate:yyyy-MM-dd} 的业务报告...");

                    var report = reportSystem.GenerateTestReport(testShopId, testDate);

                    // 显示报告结果
                    DisplayReport(report);
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序执行出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        
        static void DisplayReport(BusinessReport report)
        {
            Console.WriteLine("\n=== 业务报告结果 ===");
            Console.WriteLine($"店铺: {report.ShopName} (ID: {report.ShopId})");
            Console.WriteLine($"业务日期: {report.BusinessDate:yyyy-MM-dd}");
            Console.WriteLine($"生成时间: {report.GeneratedTime:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();
            
            // 显示时间段配置
            Console.WriteLine("时间段配置:");
            foreach (var timeSlot in report.TimeSlots)
            {
                Console.WriteLine($"  {timeSlot.TimeNo}: {timeSlot.TimeName} ({timeSlot.TimeSlotDisplay})");
            }
            Console.WriteLine();
            
            // 显示报告矩阵
            var matrix = report.GetReportMatrix();
            Console.WriteLine("业务报告矩阵 (渠道 x 时间段):");
            
            // 打印表头
            Console.Write("渠道类型".PadRight(15));
            foreach (var header in matrix.TimeSlotHeaders)
            {
                Console.Write($"{header.TimeDisplay}".PadRight(12));
            }
            Console.WriteLine("总计");
            
            // 打印分隔线
            Console.WriteLine(new string('-', 15 + matrix.TimeSlotHeaders.Count * 12 + 8));
            
            // 打印数据行
            foreach (var row in matrix.Rows)
            {
                Console.Write(row.ChannelType.PadRight(15));
                decimal rowTotal = 0;
                
                foreach (var header in matrix.TimeSlotHeaders)
                {
                    var cellData = row.TimeSlotData.ContainsKey(header.TimeNo) 
                        ? row.TimeSlotData[header.TimeNo] 
                        : new ReportCellData();
                    
                    if (row.ChannelType == "前一插槽延续")
                    {
                        Console.Write($"{cellData.Count}".PadRight(12));
                    }
                    else
                    {
                        Console.Write($"{cellData.Count}({cellData.TotalAmount:F0})".PadRight(12));
                        rowTotal += cellData.TotalAmount;
                    }
                }
                
                if (row.ChannelType != "前一插槽延续")
                {
                    Console.WriteLine($"{rowTotal:F0}");
                }
                else
                {
                    Console.WriteLine("-");
                }
            }
            
            Console.WriteLine();
            
            // 显示详细的渠道分类结果
            if (report.ChannelResults.Any())
            {
                Console.WriteLine("详细渠道分类结果:");
                var groupedResults = report.ChannelResults
                    .GroupBy(c => c.ChannelType)
                    .OrderBy(g => g.Key);
                
                foreach (var group in groupedResults)
                {
                    Console.WriteLine($"  {group.Key}:");
                    Console.WriteLine($"    订单数: {group.Count()}");
                    Console.WriteLine($"    总金额: {group.Sum(c => c.TotalAmount):F2}");
                    Console.WriteLine($"    平均金额: {group.Average(c => c.TotalAmount):F2}");
                    Console.WriteLine();
                }
            }
            else
            {
                Console.WriteLine("当前日期没有找到消费数据。");
            }
        }
    }
}

// 为了测试，我们需要创建一个简化的测试版本
namespace Sass.Pos.BusinessReport
{
    /// <summary>
    /// 测试用的简化业务报告系统
    /// </summary>
    public partial class KtvBusinessReportSystem
    {
        /// <summary>
        /// 生成测试数据的业务报告
        /// </summary>
        public BusinessReport GenerateTestReport(int shopId, DateTime businessDate)
        {
            // 创建测试时间段配置
            var timeSlots = new List<TimeSlotConfig>
            {
                new TimeSlotConfig { TimeNo = "T1", TimeName = "上午场", BegTime = 650, EndTime = 830, ShopId = shopId },
                new TimeSlotConfig { TimeNo = "T2", TimeName = "下午场", BegTime = 830, EndTime = 1010, ShopId = shopId },
                new TimeSlotConfig { TimeNo = "T3", TimeName = "晚上场", BegTime = 1010, EndTime = 1190, ShopId = shopId },
                new TimeSlotConfig { TimeNo = "T4", TimeName = "深夜场", BegTime = 1190, EndTime = 1370, ShopId = shopId }
            };
            
            // 创建测试渠道分类结果
            var channelResults = new List<ChannelClassificationResult>
            {
                new ChannelClassificationResult { InvNo = "INV001", TimeSlotNo = "T1", TimeSlotName = "上午场", ChannelType = "meituan", TotalAmount = 288 },
                new ChannelClassificationResult { InvNo = "INV002", TimeSlotNo = "T1", TimeSlotName = "上午场", ChannelType = "K+", TotalAmount = 168 },
                new ChannelClassificationResult { InvNo = "INV003", TimeSlotNo = "T2", TimeSlotName = "下午场", ChannelType = "douyin", TotalAmount = 358 },
                new ChannelClassificationResult { InvNo = "INV004", TimeSlotNo = "T2", TimeSlotName = "下午场", ChannelType = "vip_reserved", TotalAmount = 488 },
                new ChannelClassificationResult { InvNo = "INV005", TimeSlotNo = "T3", TimeSlotName = "晚上场", ChannelType = "room_fee", TotalAmount = 688 },
                new ChannelClassificationResult { InvNo = "INV006", TimeSlotNo = "T3", TimeSlotName = "晚上场", ChannelType = "meituan", TotalAmount = 388 },
                new ChannelClassificationResult { InvNo = "INV007", TimeSlotNo = "T4", TimeSlotName = "深夜场", ChannelType = "K+", TotalAmount = 588 }
            };
            
            // 创建测试延续结果
            var continuationResults = new List<PreviousSlotContinuationResult>
            {
                new PreviousSlotContinuationResult { TimeSlotNo = "T1", TimeSlotName = "上午场", ContinuationCount = 0 },
                new PreviousSlotContinuationResult { TimeSlotNo = "T2", TimeSlotName = "下午场", ContinuationCount = 1 },
                new PreviousSlotContinuationResult { TimeSlotNo = "T3", TimeSlotName = "晚上场", ContinuationCount = 2 },
                new PreviousSlotContinuationResult { TimeSlotNo = "T4", TimeSlotName = "深夜场", ContinuationCount = 1 }
            };
            
            return new BusinessReport
            {
                ShopId = shopId,
                ShopName = "天河店",
                BusinessDate = businessDate,
                TimeSlots = timeSlots,
                ChannelResults = channelResults,
                ContinuationResults = continuationResults,
                GeneratedTime = DateTime.Now
            };
        }
    }
}

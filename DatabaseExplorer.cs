using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace Sass.Pos.BusinessReport
{
    /// <summary>
    /// 数据库探索分析工具
    /// 用于连接和分析KTV业务数据库
    /// </summary>
    public class DatabaseExplorer
    {
        // 数据库连接字符串
        private readonly string _rmsConnectionString;
        private readonly string _operateDataConnectionString;
        private readonly string _dbFoodConnectionString;

        public DatabaseExplorer()
        {
            // 根据您提供的连接信息配置数据库连接字符串
            _rmsConnectionString = "Data Source=*************;Initial Catalog=rms2019;User ID=sa;Password=************;Connection Timeout=30;";
            _operateDataConnectionString = "Data Source=***********;Initial Catalog=operatedata;User ID=sa;Password=***********;Connection Timeout=30;";
            _dbFoodConnectionString = "Data Source=***********;Initial Catalog=dbfood;User ID=sa;Password=***********;Connection Timeout=30;";
        }

        #region 第一步：数据库连接测试

        /// <summary>
        /// 测试所有数据库连接
        /// </summary>
        public void TestAllConnections()
        {
            Console.WriteLine("=== 第一步：数据库连接测试 ===");
            Console.WriteLine();

            TestConnection(_rmsConnectionString, "名堂数据库 (rms2019)", "*************");
            TestConnection(_operateDataConnectionString, "数据库5 (operatedata)", "***********");
            TestConnection(_dbFoodConnectionString, "dbfood数据库 (dbfood)", "***********");
            
            Console.WriteLine("数据库连接测试完成。");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试单个数据库连接
        /// </summary>
        private bool TestConnection(string connectionString, string databaseName, string host)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    Console.WriteLine($"正在连接 {databaseName} ({host})...");
                    connection.Open();
                    
                    // 获取数据库版本信息
                    using (var command = new SqlCommand("SELECT @@VERSION", connection))
                    {
                        var version = command.ExecuteScalar()?.ToString();
                        Console.WriteLine($"✓ 连接成功！");
                        Console.WriteLine($"  数据库版本: {version?.Split('\n')[0]}");
                    }
                    
                    // 获取数据库大小信息
                    using (var command = new SqlCommand(@"
                        SELECT 
                            DB_NAME() as DatabaseName,
                            SUM(size * 8.0 / 1024) as SizeMB
                        FROM sys.database_files", connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Console.WriteLine($"  数据库大小: {reader.GetDecimal("SizeMB"):F2} MB");
                            }
                        }
                    }
                    
                    Console.WriteLine();
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 连接失败: {ex.Message}");
                Console.WriteLine();
                return false;
            }
        }

        #endregion

        #region 第二步：数据表结构分析

        /// <summary>
        /// 分析所有关键表结构
        /// </summary>
        public void AnalyzeTableStructures()
        {
            Console.WriteLine("=== 第二步：数据表结构分析 ===");
            Console.WriteLine();

            // 分析RMS数据库表
            Console.WriteLine("【RMS2019数据库表结构】");
            AnalyzeTableStructure(_rmsConnectionString, "opencacheinfo", "客人预约开台记录");
            AnalyzeTableStructure(_rmsConnectionString, "shoptimeinfo", "门店时间档位");
            AnalyzeTableStructure(_rmsConnectionString, "timeinfo", "时间档位详情");
            AnalyzeTableStructure(_rmsConnectionString, "shopinfo", "门店信息");

            // 分析operatedata数据库表
            Console.WriteLine("【OperateData数据库表结构】");
            AnalyzeTableStructure(_operateDataConnectionString, "rmcloseinfo", "结账清单数据");

            // 分析dbfood数据库表
            Console.WriteLine("【DbFood数据库表结构】");
            AnalyzeTableStructure(_dbFoodConnectionString, "RmCloseInfo", "餐饮结账信息");
            AnalyzeTableStructure(_dbFoodConnectionString, "Room", "房间信息");
            
            Console.WriteLine("数据表结构分析完成。");
            Console.WriteLine();
        }

        /// <summary>
        /// 分析单个表结构
        /// </summary>
        private void AnalyzeTableStructure(string connectionString, string tableName, string description)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine($"表名: {tableName} ({description})");
                    
                    var sql = @"
                        SELECT 
                            COLUMN_NAME,
                            DATA_TYPE,
                            CHARACTER_MAXIMUM_LENGTH,
                            IS_NULLABLE,
                            COLUMN_DEFAULT,
                            ORDINAL_POSITION
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_NAME = @TableName
                        ORDER BY ORDINAL_POSITION";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@TableName", tableName);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            Console.WriteLine("  字段名".PadRight(25) + "数据类型".PadRight(15) + "长度".PadRight(8) + "可空".PadRight(6) + "默认值");
                            Console.WriteLine("  " + new string('-', 70));
                            
                            while (reader.Read())
                            {
                                var columnName = reader.GetString("COLUMN_NAME");
                                var dataType = reader.GetString("DATA_TYPE");
                                var maxLength = reader.IsDBNull("CHARACTER_MAXIMUM_LENGTH") ? "" : reader.GetInt32("CHARACTER_MAXIMUM_LENGTH").ToString();
                                var isNullable = reader.GetString("IS_NULLABLE");
                                var defaultValue = reader.IsDBNull("COLUMN_DEFAULT") ? "" : reader.GetString("COLUMN_DEFAULT");
                                
                                Console.WriteLine($"  {columnName.PadRight(25)}{dataType.PadRight(15)}{maxLength.PadRight(8)}{isNullable.PadRight(6)}{defaultValue}");
                            }
                        }
                    }
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 分析表 {tableName} 时出错: {ex.Message}");
                Console.WriteLine();
            }
        }

        #endregion

        #region 第三步：数据内容探索

        /// <summary>
        /// 探索所有关键表的数据内容
        /// </summary>
        public void ExploreTableData()
        {
            Console.WriteLine("=== 第三步：数据内容探索 ===");
            Console.WriteLine();

            // 探索RMS数据库表数据
            Console.WriteLine("【RMS2019数据库数据探索】");
            ExploreTableContent(_rmsConnectionString, "opencacheinfo", "客人预约开台记录");
            ExploreTableContent(_rmsConnectionString, "shoptimeinfo", "门店时间档位");
            ExploreTableContent(_rmsConnectionString, "timeinfo", "时间档位详情");
            ExploreTableContent(_rmsConnectionString, "shopinfo", "门店信息");

            // 探索operatedata数据库表数据
            Console.WriteLine("【OperateData数据库数据探索】");
            ExploreTableContent(_operateDataConnectionString, "rmcloseinfo", "结账清单数据");

            // 探索dbfood数据库表数据
            Console.WriteLine("【DbFood数据库数据探索】");
            ExploreTableContent(_dbFoodConnectionString, "RmCloseInfo", "餐饮结账信息");
            
            Console.WriteLine("数据内容探索完成。");
            Console.WriteLine();
        }

        /// <summary>
        /// 探索单个表的数据内容
        /// </summary>
        private void ExploreTableContent(string connectionString, string tableName, string description)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine($"表: {tableName} ({description})");
                    
                    // 获取记录总数
                    using (var command = new SqlCommand($"SELECT COUNT(*) FROM [{tableName}]", connection))
                    {
                        var totalCount = command.ExecuteScalar();
                        Console.WriteLine($"  总记录数: {totalCount}");
                    }
                    
                    // 获取示例数据
                    using (var command = new SqlCommand($"SELECT TOP 5 * FROM [{tableName}]", connection))
                    using (var reader = command.ExecuteReader())
                    {
                        Console.WriteLine("  示例数据 (前5条记录):");
                        
                        if (reader.HasRows)
                        {
                            // 打印列名
                            var columnNames = new List<string>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                columnNames.Add(reader.GetName(i));
                            }
                            Console.WriteLine("    " + string.Join(" | ", columnNames));
                            Console.WriteLine("    " + new string('-', columnNames.Count * 15));
                            
                            // 打印数据行
                            int rowCount = 0;
                            while (reader.Read() && rowCount < 3) // 只显示前3行以节省空间
                            {
                                var values = new List<string>();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    var value = reader.IsDBNull(i) ? "NULL" : reader.GetValue(i).ToString();
                                    values.Add(value.Length > 12 ? value.Substring(0, 12) + "..." : value);
                                }
                                Console.WriteLine("    " + string.Join(" | ", values));
                                rowCount++;
                            }
                        }
                        else
                        {
                            Console.WriteLine("    表为空");
                        }
                    }
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 探索表 {tableName} 时出错: {ex.Message}");
                Console.WriteLine();
            }
        }

        #endregion

        #region 第四步：数据关联分析

        /// <summary>
        /// 分析数据关联关系
        /// </summary>
        public void AnalyzeDataRelationships()
        {
            Console.WriteLine("=== 第四步：数据关联分析 ===");
            Console.WriteLine();

            AnalyzeRmCloseInfoDetails();
            AnalyzeShopTimeConfiguration();
            AnalyzeDataRelationshipsByInvNo();
            AnalyzePaymentChannelDistribution();
            CheckMissingOpenTimeData();

            Console.WriteLine("数据关联分析完成。");
            Console.WriteLine();
        }

        /// <summary>
        /// 详细分析rmcloseinfo表
        /// </summary>
        private void AnalyzeRmCloseInfoDetails()
        {
            Console.WriteLine("【rmcloseinfo表详细分析】");

            try
            {
                using (var connection = new SqlConnection(_operateDataConnectionString))
                {
                    connection.Open();

                    // 分析数据分布
                    var sql = @"
                        SELECT
                            COUNT(*) as TotalRecords,
                            COUNT(DISTINCT ShopId) as UniqueShops,
                            COUNT(DISTINCT InvNo) as UniqueInvoices,
                            MIN(CloseDatetime) as EarliestRecord,
                            MAX(CloseDatetime) as LatestRecord,
                            AVG(CAST(Cash + Vesa + WXPay + AliPay + MTPay + DZPay + NMPay + Coupon + RechargeAccount as FLOAT)) as AvgAmount
                        FROM rmcloseinfo";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Console.WriteLine($"  总记录数: {reader["TotalRecords"]}");
                            Console.WriteLine($"  涉及店铺数: {reader["UniqueShops"]}");
                            Console.WriteLine($"  唯一发票号: {reader["UniqueInvoices"]}");
                            Console.WriteLine($"  最早记录: {reader["EarliestRecord"]}");
                            Console.WriteLine($"  最新记录: {reader["LatestRecord"]}");
                            Console.WriteLine($"  平均消费金额: {Convert.ToDecimal(reader["AvgAmount"]):F2}");
                        }
                    }

                    // 按店铺分析数据分布
                    Console.WriteLine("\n  各店铺数据分布:");
                    sql = @"
                        SELECT TOP 10
                            ShopId,
                            COUNT(*) as RecordCount,
                            MIN(CloseDatetime) as EarliestDate,
                            MAX(CloseDatetime) as LatestDate,
                            SUM(Cash + Vesa + WXPay + AliPay + MTPay + DZPay + NMPay + Coupon + RechargeAccount) as TotalAmount
                        FROM rmcloseinfo
                        GROUP BY ShopId
                        ORDER BY RecordCount DESC";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        Console.WriteLine("    店铺ID | 记录数 | 最早日期 | 最新日期 | 总金额");
                        Console.WriteLine("    " + new string('-', 60));
                        while (reader.Read())
                        {
                            Console.WriteLine($"    {reader["ShopId"].ToString().PadRight(6)} | {reader["RecordCount"].ToString().PadRight(6)} | {Convert.ToDateTime(reader["EarliestDate"]):yyyy-MM-dd} | {Convert.ToDateTime(reader["LatestDate"]):yyyy-MM-dd} | {Convert.ToDecimal(reader["TotalAmount"]):F0}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 分析rmcloseinfo表时出错: {ex.Message}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 分析店铺时间配置
        /// </summary>
        private void AnalyzeShopTimeConfiguration()
        {
            Console.WriteLine("【店铺时间配置分析】");

            try
            {
                using (var connection = new SqlConnection(_rmsConnectionString))
                {
                    connection.Open();

                    var sql = @"
                        SELECT
                            s.ShopId,
                            si.ShopName,
                            t.TimeNo,
                            t.TimeName,
                            t.BegTime,
                            t.EndTime,
                            CAST(t.BegTime / 60 as VARCHAR) + ':' + RIGHT('0' + CAST(t.BegTime % 60 as VARCHAR), 2) as StartTime,
                            CAST(t.EndTime / 60 as VARCHAR) + ':' + RIGHT('0' + CAST(t.EndTime % 60 as VARCHAR), 2) as EndTime
                        FROM shoptimeinfo s
                        INNER JOIN timeinfo t ON s.TimeNo = t.TimeNo
                        LEFT JOIN shopinfo si ON s.ShopId = si.ShopId
                        ORDER BY s.ShopId, t.BegTime";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        Console.WriteLine("  店铺时间段配置:");
                        Console.WriteLine("    店铺ID | 店铺名 | 时间段号 | 时间段名 | 开始时间 | 结束时间");
                        Console.WriteLine("    " + new string('-', 70));

                        while (reader.Read())
                        {
                            var shopId = reader["ShopId"].ToString();
                            var shopName = reader.IsDBNull("ShopName") ? "未知" : reader["ShopName"].ToString();
                            var timeNo = reader["TimeNo"].ToString();
                            var timeName = reader["TimeName"].ToString();
                            var startTime = reader["StartTime"].ToString();
                            var endTime = reader["EndTime"].ToString();

                            Console.WriteLine($"    {shopId.PadRight(6)} | {shopName.PadRight(8)} | {timeNo.PadRight(8)} | {timeName.PadRight(8)} | {startTime.PadRight(8)} | {endTime}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 分析店铺时间配置时出错: {ex.Message}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 通过InvNo分析数据关联关系
        /// </summary>
        private void AnalyzeDataRelationshipsByInvNo()
        {
            Console.WriteLine("【通过InvNo分析数据关联】");

            try
            {
                // 从rmcloseinfo获取一些InvNo样本
                using (var connection = new SqlConnection(_operateDataConnectionString))
                {
                    connection.Open();

                    var sql = "SELECT TOP 5 InvNo, ShopId, CloseDatetime FROM rmcloseinfo ORDER BY CloseDatetime DESC";
                    var sampleInvNos = new List<(string InvNo, int ShopId, DateTime CloseTime)>();

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            sampleInvNos.Add((
                                reader.GetString("InvNo"),
                                reader.GetInt32("ShopId"),
                                reader.GetDateTime("CloseDatetime")
                            ));
                        }
                    }

                    Console.WriteLine("  样本InvNo在各表中的关联情况:");
                    foreach (var sample in sampleInvNos)
                    {
                        Console.WriteLine($"    InvNo: {sample.InvNo} (店铺{sample.ShopId}, {sample.CloseTime:yyyy-MM-dd HH:mm})");

                        // 检查在opencacheinfo中是否存在
                        CheckInvNoInTable(_rmsConnectionString, "opencacheinfo", "Invno", sample.InvNo);

                        // 检查在dbfood的RmCloseInfo中是否存在
                        CheckInvNoInTable(_dbFoodConnectionString, "RmCloseInfo", "InvNo", sample.InvNo);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 分析数据关联时出错: {ex.Message}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 检查InvNo在指定表中是否存在
        /// </summary>
        private void CheckInvNoInTable(string connectionString, string tableName, string columnName, string invNo)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    var sql = $"SELECT COUNT(*) FROM [{tableName}] WHERE [{columnName}] = @InvNo";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@InvNo", invNo);
                        var count = Convert.ToInt32(command.ExecuteScalar());
                        Console.WriteLine($"      在{tableName}表中: {(count > 0 ? "存在" : "不存在")} ({count}条)");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      在{tableName}表中: 查询出错 - {ex.Message}");
            }
        }

        /// <summary>
        /// 分析支付渠道分布
        /// </summary>
        private void AnalyzePaymentChannelDistribution()
        {
            Console.WriteLine("【支付渠道分布分析】");

            try
            {
                using (var connection = new SqlConnection(_operateDataConnectionString))
                {
                    connection.Open();

                    var sql = @"
                        SELECT
                            SUM(CASE WHEN Cash > 0 THEN 1 ELSE 0 END) as CashCount,
                            SUM(CASE WHEN WXPay > 0 THEN 1 ELSE 0 END) as WXPayCount,
                            SUM(CASE WHEN AliPay > 0 THEN 1 ELSE 0 END) as AliPayCount,
                            SUM(CASE WHEN MTPay > 0 THEN 1 ELSE 0 END) as MTPayCount,
                            SUM(CASE WHEN DZPay > 0 THEN 1 ELSE 0 END) as DZPayCount,
                            SUM(CASE WHEN Vesa > 0 THEN 1 ELSE 0 END) as VesaCount,
                            SUM(CASE WHEN Coupon > 0 THEN 1 ELSE 0 END) as CouponCount,
                            SUM(CASE WHEN RechargeAccount > 0 THEN 1 ELSE 0 END) as RechargeCount,
                            SUM(Cash) as TotalCash,
                            SUM(WXPay) as TotalWXPay,
                            SUM(AliPay) as TotalAliPay,
                            SUM(MTPay) as TotalMTPay,
                            SUM(DZPay) as TotalDZPay,
                            SUM(Vesa) as TotalVesa
                        FROM rmcloseinfo";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Console.WriteLine("  支付方式使用统计:");
                            Console.WriteLine($"    现金支付: {reader["CashCount"]}笔, 总额: {Convert.ToDecimal(reader["TotalCash"]):F0}");
                            Console.WriteLine($"    微信支付: {reader["WXPayCount"]}笔, 总额: {Convert.ToDecimal(reader["TotalWXPay"]):F0}");
                            Console.WriteLine($"    支付宝: {reader["AliPayCount"]}笔, 总额: {Convert.ToDecimal(reader["TotalAliPay"]):F0}");
                            Console.WriteLine($"    美团支付: {reader["MTPayCount"]}笔, 总额: {Convert.ToDecimal(reader["TotalMTPay"]):F0}");
                            Console.WriteLine($"    抖音支付: {reader["DZPayCount"]}笔, 总额: {Convert.ToDecimal(reader["TotalDZPay"]):F0}");
                            Console.WriteLine($"    会员卡: {reader["VesaCount"]}笔, 总额: {Convert.ToDecimal(reader["TotalVesa"]):F0}");
                            Console.WriteLine($"    优惠券: {reader["CouponCount"]}笔");
                            Console.WriteLine($"    充值账户: {reader["RechargeCount"]}笔");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 分析支付渠道分布时出错: {ex.Message}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 检查缺失的开台时间数据
        /// </summary>
        private void CheckMissingOpenTimeData()
        {
            Console.WriteLine("【开台时间数据检查】");

            // 检查rmcloseinfo表是否有开台时间字段
            Console.WriteLine("  检查rmcloseinfo表中的时间相关字段:");
            try
            {
                using (var connection = new SqlConnection(_operateDataConnectionString))
                {
                    connection.Open();
                    var sql = @"
                        SELECT COLUMN_NAME, DATA_TYPE
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_NAME = 'rmcloseinfo'
                        AND (COLUMN_NAME LIKE '%time%' OR COLUMN_NAME LIKE '%date%' OR COLUMN_NAME LIKE '%open%' OR COLUMN_NAME LIKE '%start%')
                        ORDER BY COLUMN_NAME";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        var hasOpenTimeField = false;
                        while (reader.Read())
                        {
                            var columnName = reader.GetString("COLUMN_NAME");
                            var dataType = reader.GetString("DATA_TYPE");
                            Console.WriteLine($"    {columnName} ({dataType})");

                            if (columnName.ToLower().Contains("open") || columnName.ToLower().Contains("start"))
                            {
                                hasOpenTimeField = true;
                            }
                        }

                        if (!hasOpenTimeField)
                        {
                            Console.WriteLine("    ⚠️ 未发现开台时间相关字段");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ✗ 检查时间字段时出错: {ex.Message}");
            }

            // 检查opencacheinfo表中的时间信息
            Console.WriteLine("\n  检查opencacheinfo表中的时间信息:");
            try
            {
                using (var connection = new SqlConnection(_rmsConnectionString))
                {
                    connection.Open();
                    var sql = @"
                        SELECT TOP 5
                            Invno,
                            ComeDate,
                            ComeTime,
                            BookDateTime
                        FROM opencacheinfo
                        WHERE Invno IS NOT NULL AND Invno != ''
                        ORDER BY BookDateTime DESC";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        Console.WriteLine("    发票号 | 来店日期 | 来店时间 | 预约时间");
                        Console.WriteLine("    " + new string('-', 50));
                        while (reader.Read())
                        {
                            var invno = reader.IsDBNull("Invno") ? "NULL" : reader.GetString("Invno");
                            var comeDate = reader.IsDBNull("ComeDate") ? "NULL" : reader.GetString("ComeDate");
                            var comeTime = reader.IsDBNull("ComeTime") ? "NULL" : reader.GetString("ComeTime");
                            var bookDateTime = reader.IsDBNull("BookDateTime") ? "NULL" : reader.GetDateTime("BookDateTime").ToString("yyyy-MM-dd HH:mm");

                            Console.WriteLine($"    {invno.PadRight(8)} | {comeDate.PadRight(10)} | {comeTime.PadRight(8)} | {bookDateTime}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ✗ 检查opencacheinfo时间信息时出错: {ex.Message}");
            }

            Console.WriteLine("\n  💡 建议:");
            Console.WriteLine("    1. rmcloseinfo表缺少开台时间字段，建议添加OpenDateTime字段");
            Console.WriteLine("    2. 可以通过InvNo关联opencacheinfo表获取开台相关时间信息");
            Console.WriteLine("    3. 或者从其他业务系统表中获取房间开台时间");
            Console.WriteLine();
        }

        #endregion

        /// <summary>
        /// 执行完整的数据库探索分析
        /// </summary>
        public void RunCompleteAnalysis()
        {
            Console.WriteLine("开始执行KTV业务数据库完整探索分析...");
            Console.WriteLine("=" * 60);
            Console.WriteLine();

            TestAllConnections();
            AnalyzeTableStructures();
            ExploreTableData();
            AnalyzeDataRelationships();

            Console.WriteLine("=" * 60);
            Console.WriteLine("数据库探索分析完成！");
        }
    }
}

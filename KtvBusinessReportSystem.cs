using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace Sass.Pos.BusinessReport
{
    /// <summary>
    /// KTV业务报告系统
    /// 用于分析不同时间段和渠道的客户消费数据
    /// </summary>
    public class KtvBusinessReportSystem
    {
        private readonly string _rmsConnectionString;
        private readonly string _operateDataConnectionString;

        public KtvBusinessReportSystem()
        {
            // 根据您提供的连接信息配置数据库连接字符串
            _rmsConnectionString = "Data Source=193.112.2.229;Initial Catalog=rms2019;User ID=sa;Password=********************************;";
            _operateDataConnectionString = "Data Source=192.168.2.5;Initial Catalog=operatedata;User ID=sa;Password=********************************;";
        }

        #region 步骤1：提取报告所需的关键数据

        /// <summary>
        /// 获取商店和业务日期信息
        /// </summary>
        public List<ShopBusinessInfo> GetShopBusinessInfo(int shopId, DateTime businessDate)
        {
            var result = new List<ShopBusinessInfo>();
            
            using (var connection = new SqlConnection(_rmsConnectionString))
            {
                connection.Open();
                var sql = @"
                    SELECT DISTINCT 
                        s.ShopId,
                        s.ShopName,
                        @BusinessDate as BusinessDate
                    FROM shopinfo s 
                    WHERE s.ShopId = @ShopId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@ShopId", shopId);
                    command.Parameters.AddWithValue("@BusinessDate", businessDate);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new ShopBusinessInfo
                            {
                                ShopId = reader.GetInt32("ShopId"),
                                ShopName = reader.GetString("ShopName"),
                                BusinessDate = reader.GetDateTime("BusinessDate")
                            });
                        }
                    }
                }
            }
            
            return result;
        }

        /// <summary>
        /// 获取消费时间戳信息（开台时间和结账时间）
        /// </summary>
        public List<ConsumptionTimeInfo> GetConsumptionTimeInfo(int shopId, DateTime businessDate)
        {
            var result = new List<ConsumptionTimeInfo>();
            
            // 从operatedata数据库的rmcloseinfo表获取结账信息
            using (var connection = new SqlConnection(_operateDataConnectionString))
            {
                connection.Open();
                var sql = @"
                    SELECT 
                        r.InvNo,
                        r.CloseDatetime,
                        r.ShopId,
                        r.Cash + r.Vesa + r.WXPay + r.AliPay + r.MTPay + r.DZPay + r.NMPay + r.Coupon + r.RechargeAccount as TotalAmount,
                        -- 需要添加开台时间字段，如果不存在则需要从其他表关联获取
                        NULL as OpenDateTime
                    FROM rmcloseinfo r
                    WHERE r.ShopId = @ShopId 
                        AND CAST(r.CloseDatetime AS DATE) = @BusinessDate";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@ShopId", shopId);
                    command.Parameters.AddWithValue("@BusinessDate", businessDate.Date);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new ConsumptionTimeInfo
                            {
                                InvNo = reader.GetString("InvNo"),
                                OpenDateTime = reader.IsDBNull("OpenDateTime") ? (DateTime?)null : reader.GetDateTime("OpenDateTime"),
                                CloseDateTime = reader.GetDateTime("CloseDatetime"),
                                ShopId = reader.GetInt32("ShopId"),
                                TotalAmount = reader.GetDecimal("TotalAmount")
                            });
                        }
                    }
                }
            }
            
            return result;
        }

        /// <summary>
        /// 获取渠道信息
        /// </summary>
        public List<ChannelInfo> GetChannelInfo(int shopId, DateTime businessDate)
        {
            var result = new List<ChannelInfo>();
            
            using (var connection = new SqlConnection(_operateDataConnectionString))
            {
                connection.Open();
                var sql = @"
                    SELECT 
                        r.InvNo,
                        CASE 
                            WHEN r.MTPay > 0 THEN 'meituan'
                            WHEN r.DZPay > 0 THEN 'douyin'
                            WHEN r.WXPay > 0 OR r.AliPay > 0 THEN 'online_payment'
                            WHEN r.Vesa > 0 THEN 'vip_reserved'
                            WHEN r.Cash > 0 THEN 'room_fee'
                            ELSE 'K+'
                        END as ChannelType,
                        r.ShopId
                    FROM rmcloseinfo r
                    WHERE r.ShopId = @ShopId 
                        AND CAST(r.CloseDatetime AS DATE) = @BusinessDate";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@ShopId", shopId);
                    command.Parameters.AddWithValue("@BusinessDate", businessDate.Date);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new ChannelInfo
                            {
                                InvNo = reader.GetString("InvNo"),
                                ChannelType = reader.GetString("ChannelType"),
                                ShopId = reader.GetInt32("ShopId")
                            });
                        }
                    }
                }
            }
            
            return result;
        }

        /// <summary>
        /// 获取时间段配置信息
        /// </summary>
        public List<TimeSlotConfig> GetTimeSlotConfig(int shopId)
        {
            var result = new List<TimeSlotConfig>();
            
            using (var connection = new SqlConnection(_rmsConnectionString))
            {
                connection.Open();
                var sql = @"
                    SELECT 
                        t.TimeNo,
                        t.TimeName,
                        t.BegTime,
                        t.EndTime,
                        st.ShopId
                    FROM timeinfo t
                    INNER JOIN shoptimeinfo st ON t.TimeNo = st.TimeNo
                    WHERE st.ShopId = @ShopId
                    ORDER BY t.BegTime";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@ShopId", shopId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new TimeSlotConfig
                            {
                                TimeNo = reader.GetString("TimeNo"),
                                TimeName = reader.GetString("TimeName"),
                                BegTime = reader.GetInt32("BegTime"),
                                EndTime = reader.GetInt32("EndTime"),
                                ShopId = reader.GetInt32("ShopId")
                            });
                        }
                    }
                }
            }
            
            return result;
        }

        #endregion

        #region 步骤2：业务逻辑匹配和分类

        /// <summary>
        /// 执行时间段重叠检测
        /// </summary>
        public List<TimeSlotOverlapResult> DetectTimeSlotOverlap(
            List<ConsumptionTimeInfo> consumptions, 
            List<TimeSlotConfig> timeSlots)
        {
            var results = new List<TimeSlotOverlapResult>();

            foreach (var consumption in consumptions)
            {
                if (!consumption.OpenDateTime.HasValue) continue;

                var consumptionStart = consumption.OpenDateTime.Value.TimeOfDay;
                var consumptionEnd = consumption.CloseDateTime.TimeOfDay;

                foreach (var timeSlot in timeSlots)
                {
                    var slotStart = TimeSpan.FromMinutes(timeSlot.BegTime);
                    var slotEnd = TimeSpan.FromMinutes(timeSlot.EndTime);

                    // 检查时间重叠
                    if (IsTimeOverlap(consumptionStart, consumptionEnd, slotStart, slotEnd))
                    {
                        results.Add(new TimeSlotOverlapResult
                        {
                            InvNo = consumption.InvNo,
                            TimeSlotNo = timeSlot.TimeNo,
                            TimeSlotName = timeSlot.TimeName,
                            ConsumptionStart = consumptionStart,
                            ConsumptionEnd = consumptionEnd,
                            SlotStart = slotStart,
                            SlotEnd = slotEnd,
                            TotalAmount = consumption.TotalAmount
                        });
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// 检查两个时间段是否重叠
        /// </summary>
        private bool IsTimeOverlap(TimeSpan start1, TimeSpan end1, TimeSpan start2, TimeSpan end2)
        {
            return start1 < end2 && start2 < end1;
        }

        /// <summary>
        /// 应用渠道标签
        /// </summary>
        public List<ChannelClassificationResult> ApplyChannelLabels(
            List<TimeSlotOverlapResult> overlapResults,
            List<ChannelInfo> channelInfos)
        {
            var results = new List<ChannelClassificationResult>();

            foreach (var overlap in overlapResults)
            {
                var channelInfo = channelInfos.FirstOrDefault(c => c.InvNo == overlap.InvNo);
                
                results.Add(new ChannelClassificationResult
                {
                    InvNo = overlap.InvNo,
                    TimeSlotNo = overlap.TimeSlotNo,
                    TimeSlotName = overlap.TimeSlotName,
                    ChannelType = channelInfo?.ChannelType ?? "K+",
                    TotalAmount = overlap.TotalAmount
                });
            }

            return results;
        }

        /// <summary>
        /// 确定前一插槽延续批次
        /// </summary>
        public List<PreviousSlotContinuationResult> DeterminePreviousSlotContinuation(
            List<ConsumptionTimeInfo> consumptions,
            List<TimeSlotConfig> timeSlots)
        {
            var results = new List<PreviousSlotContinuationResult>();

            foreach (var timeSlot in timeSlots)
            {
                var slotStart = TimeSpan.FromMinutes(timeSlot.BegTime);
                int continuationCount = 0;

                foreach (var consumption in consumptions)
                {
                    if (!consumption.OpenDateTime.HasValue) continue;

                    var openTime = consumption.OpenDateTime.Value.TimeOfDay;
                    var closeTime = consumption.CloseDateTime.TimeOfDay;

                    // 如果开台时间在时间段开始之前，结账时间在时间段开始之后
                    if (openTime < slotStart && closeTime > slotStart)
                    {
                        continuationCount++;
                    }
                }

                results.Add(new PreviousSlotContinuationResult
                {
                    TimeSlotNo = timeSlot.TimeNo,
                    TimeSlotName = timeSlot.TimeName,
                    ContinuationCount = continuationCount
                });
            }

            return results;
        }

        #endregion

        #region 步骤3：生成业务报告

        /// <summary>
        /// 生成完整的业务报告
        /// </summary>
        public BusinessReport GenerateBusinessReport(int shopId, DateTime businessDate)
        {
            // 步骤1：提取关键数据
            var shopInfo = GetShopBusinessInfo(shopId, businessDate).FirstOrDefault();
            var consumptions = GetConsumptionTimeInfo(shopId, businessDate);
            var channels = GetChannelInfo(shopId, businessDate);
            var timeSlots = GetTimeSlotConfig(shopId);

            // 步骤2：业务逻辑匹配
            var overlapResults = DetectTimeSlotOverlap(consumptions, timeSlots);
            var channelResults = ApplyChannelLabels(overlapResults, channels);
            var continuationResults = DeterminePreviousSlotContinuation(consumptions, timeSlots);

            // 步骤3：生成报告
            var report = new BusinessReport
            {
                ShopId = shopId,
                ShopName = shopInfo?.ShopName ?? "未知店铺",
                BusinessDate = businessDate,
                TimeSlots = timeSlots,
                ChannelResults = channelResults,
                ContinuationResults = continuationResults,
                GeneratedTime = DateTime.Now
            };

            return report;
        }

        #endregion

        #region 报告导出功能

        /// <summary>
        /// 导出报告为HTML格式
        /// </summary>
        public string ExportToHtml(BusinessReport report)
        {
            var matrix = report.GetReportMatrix();
            var html = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>KTV业务报告 - {report.ShopName} - {report.BusinessDate:yyyy-MM-dd}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .info {{ margin-bottom: 20px; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #f2f2f2; font-weight: bold; }}
        .channel-header {{ background-color: #e6f3ff; text-align: left; font-weight: bold; }}
        .continuation-row {{ background-color: #fff2e6; }}
        .amount {{ color: #007700; }}
        .count {{ color: #000077; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>KTV业务报告</h1>
        <h2>{report.ShopName} (店铺ID: {report.ShopId})</h2>
    </div>

    <div class='info'>
        <p><strong>业务日期:</strong> {report.BusinessDate:yyyy年MM月dd日}</p>
        <p><strong>生成时间:</strong> {report.GeneratedTime:yyyy-MM-dd HH:mm:ss}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>渠道类型</th>";

            foreach (var header in matrix.TimeSlotHeaders)
            {
                html += $"<th>{header.TimeName}<br/>({header.TimeDisplay})</th>";
            }
            html += "<th>总计</th></tr></thead><tbody>";

            foreach (var row in matrix.Rows)
            {
                var cssClass = row.ChannelType == "前一插槽延续" ? "continuation-row" : "";
                html += $"<tr class='{cssClass}'>";
                html += $"<td class='channel-header'>{row.ChannelType}</td>";

                decimal rowTotal = 0;
                foreach (var header in matrix.TimeSlotHeaders)
                {
                    var cellData = row.TimeSlotData.ContainsKey(header.TimeNo)
                        ? row.TimeSlotData[header.TimeNo]
                        : new ReportCellData();

                    if (row.ChannelType == "前一插槽延续")
                    {
                        html += $"<td><span class='count'>{cellData.Count}</span></td>";
                    }
                    else
                    {
                        html += $"<td><span class='count'>{cellData.Count}</span><br/><span class='amount'>¥{cellData.TotalAmount:F0}</span></td>";
                        rowTotal += cellData.TotalAmount;
                    }
                }

                if (row.ChannelType != "前一插槽延续")
                {
                    html += $"<td><span class='amount'>¥{rowTotal:F0}</span></td>";
                }
                else
                {
                    html += "<td>-</td>";
                }
                html += "</tr>";
            }

            html += @"
        </tbody>
    </table>

    <div class='info'>
        <h3>说明:</h3>
        <ul>
            <li>数字表示该时间段该渠道的订单数量</li>
            <li>金额表示该时间段该渠道的总消费金额</li>
            <li>前一插槽延续表示从上一个时间段延续到当前时间段的订单数量</li>
        </ul>
    </div>
</body>
</html>";

            return html;
        }

        /// <summary>
        /// 导出报告为CSV格式
        /// </summary>
        public string ExportToCsv(BusinessReport report)
        {
            var matrix = report.GetReportMatrix();
            var csv = "渠道类型";

            // 添加时间段标题
            foreach (var header in matrix.TimeSlotHeaders)
            {
                csv += $",{header.TimeName}({header.TimeDisplay})";
            }
            csv += ",总计\n";

            // 添加数据行
            foreach (var row in matrix.Rows)
            {
                csv += row.ChannelType;
                decimal rowTotal = 0;

                foreach (var header in matrix.TimeSlotHeaders)
                {
                    var cellData = row.TimeSlotData.ContainsKey(header.TimeNo)
                        ? row.TimeSlotData[header.TimeNo]
                        : new ReportCellData();

                    if (row.ChannelType == "前一插槽延续")
                    {
                        csv += $",{cellData.Count}";
                    }
                    else
                    {
                        csv += $",{cellData.Count}({cellData.TotalAmount:F0})";
                        rowTotal += cellData.TotalAmount;
                    }
                }

                if (row.ChannelType != "前一插槽延续")
                {
                    csv += $",{rowTotal:F0}";
                }
                else
                {
                    csv += ",-";
                }
                csv += "\n";
            }

            return csv;
        }

        #endregion

        #region 数据修复和增强功能

        /// <summary>
        /// 为rmcloseinfo表添加开台时间字段的建议SQL
        /// </summary>
        public string GetAddOpenTimeFieldSql()
        {
            return @"
-- 为rmcloseinfo表添加开台时间字段
ALTER TABLE rmcloseinfo
ADD OpenDateTime DATETIME NULL;

-- 创建索引以提高查询性能
CREATE INDEX IX_rmcloseinfo_OpenDateTime ON rmcloseinfo(OpenDateTime);
CREATE INDEX IX_rmcloseinfo_ShopId_CloseDatetime ON rmcloseinfo(ShopId, CloseDatetime);

-- 如果可以从其他表关联获取开台时间，可以使用类似以下的更新语句
-- UPDATE r SET r.OpenDateTime = o.某个开台时间字段
-- FROM rmcloseinfo r
-- INNER JOIN 其他表 o ON r.InvNo = o.InvNo
-- WHERE r.OpenDateTime IS NULL;
";
        }

        /// <summary>
        /// 获取数据质量检查SQL
        /// </summary>
        public string GetDataQualityCheckSql()
        {
            return @"
-- 数据质量检查SQL

-- 1. 检查缺失开台时间的记录数量
SELECT COUNT(*) as MissingOpenTimeCount
FROM rmcloseinfo
WHERE OpenDateTime IS NULL;

-- 2. 检查异常的消费时长（结账时间早于开台时间）
SELECT COUNT(*) as InvalidDurationCount
FROM rmcloseinfo
WHERE OpenDateTime IS NOT NULL
  AND CloseDatetime < OpenDateTime;

-- 3. 检查各店铺的数据分布
SELECT
    ShopId,
    COUNT(*) as TotalRecords,
    MIN(CloseDatetime) as EarliestRecord,
    MAX(CloseDatetime) as LatestRecord,
    SUM(CASE WHEN OpenDateTime IS NULL THEN 1 ELSE 0 END) as MissingOpenTime
FROM rmcloseinfo
GROUP BY ShopId
ORDER BY ShopId;

-- 4. 检查支付方式分布
SELECT
    SUM(CASE WHEN Cash > 0 THEN 1 ELSE 0 END) as CashPayments,
    SUM(CASE WHEN WXPay > 0 THEN 1 ELSE 0 END) as WeChatPayments,
    SUM(CASE WHEN AliPay > 0 THEN 1 ELSE 0 END) as AliPayments,
    SUM(CASE WHEN MTPay > 0 THEN 1 ELSE 0 END) as MeituanPayments,
    SUM(CASE WHEN DZPay > 0 THEN 1 ELSE 0 END) as DouyinPayments,
    SUM(CASE WHEN Vesa > 0 THEN 1 ELSE 0 END) as VesaPayments
FROM rmcloseinfo;
";
        }

        #endregion
    }
}

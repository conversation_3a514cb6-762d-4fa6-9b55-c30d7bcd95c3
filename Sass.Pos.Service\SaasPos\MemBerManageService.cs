using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Common.Factory;
using Saas.Pos.Domain.IService.SaasPos;
using Saas.Pos.Drive.MIMS;
using Saas.Pos.Drive.MIMS.MemberManage;
using Saas.Pos.Drive.MIMS.MemberTopUpManage;
using Saas.Pos.Model.General;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Service.SaasPos
{
    public partial class SaasPosService : IMemBerManageService
    {
        public ResponseContext<MemberInfoData> GetMemBerData(GetMemBerDataContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.GetMemBerData(context);
        }

        /// <summary>
        /// 查询会员明细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetMemBerDetailDataModel> GetMemBerDetailData(GetMemBerDetailDataContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.GetMemBerDetailData(context);
        }

        /// <summary>
        /// 结账
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RepDeductMoneyModel> DeductMoney(MemBerDeductMoneyContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.Bdm.DeductMoney(context);
        }

        /// <summary>
        /// 人工解除风控
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RepDeductMoneyModel> LabourRelieveMemBerRisk(LabourRelieveMemBerRiskContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.Mbr.LabourRelieveMemBerRisk(context);
        }

        /// <summary>
        /// 查询会员返还规则
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetMemBerRuleDataModel> GetMemBerRuleData(GetMemBerRuleDataContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.Bdm.GetMemBerRuleData(context);
        }

        /// <summary>
        /// 调用提成绑定存储过程
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<Model.MIMS.Model.MemberTopUpEditModel> MemberTopUpEdit(Model.MIMS.Context.MemberTopUpEditContext context)
        {
            var drive = new MemberTopUpDrive();
            return drive.MemberTopUpEdit(context);
        }

        public ResponseContext<List<GetMemberCardRecordDataModel>> GetMemberCardRecordData(GetMemberCardRecordDataContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.Mbc.GetMemberCardRecordData(context);

        }

        public ResponseContext<ExcelMemberCardRecordDataModel> ExcelMemberCardRecordData(ExcelMemberCardRecordDataContext context)
        {
            var drive = new MemBerManageDrive();
            return drive.Mbc.ExcelMemberCardRecordData(context);
        }

        public ResponseContext<List<KeyValueModel>> GetMemberLevelList()
        {
            var drive = new MemberInfoDrive();
            return drive.GetMemberLevelList();
        }
    }
}

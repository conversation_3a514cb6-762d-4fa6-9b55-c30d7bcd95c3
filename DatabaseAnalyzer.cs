using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Sass.Pos.BusinessReport
{
    /// <summary>
    /// 数据库结构分析工具
    /// 用于分析和查看数据库表结构
    /// </summary>
    public class DatabaseAnalyzer
    {
        private readonly string _rmsConnectionString;
        private readonly string _operateDataConnectionString;

        public DatabaseAnalyzer()
        {
            // 根据您提供的连接信息配置数据库连接字符串
            _rmsConnectionString = "Data Source=193.112.2.229;Initial Catalog=rms2019;User ID=sa;Password=********************************;";
            _operateDataConnectionString = "Data Source=192.168.2.5;Initial Catalog=operatedata;User ID=sa;Password=********************************;";
        }

        /// <summary>
        /// 获取表结构信息
        /// </summary>
        public List<TableStructureInfo> GetTableStructure(string connectionString, string tableName)
        {
            var result = new List<TableStructureInfo>();
            
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                var sql = @"
                    SELECT 
                        TABLE_NAME,
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = @TableName
                    ORDER BY ORDINAL_POSITION";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@TableName", tableName);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new TableStructureInfo
                            {
                                TableName = reader.GetString("TABLE_NAME"),
                                ColumnName = reader.GetString("COLUMN_NAME"),
                                DataType = reader.GetString("DATA_TYPE"),
                                IsNullable = reader.GetString("IS_NULLABLE") == "YES",
                                DefaultValue = reader.IsDBNull("COLUMN_DEFAULT") ? null : reader.GetString("COLUMN_DEFAULT")
                            });
                        }
                    }
                }
            }
            
            return result;
        }

        /// <summary>
        /// 获取RMS数据库中的关键表结构
        /// </summary>
        public Dictionary<string, List<TableStructureInfo>> GetRmsTableStructures()
        {
            var tables = new[] { "opencacheinfo", "shoptimeinfo", "timeinfo", "shopinfo" };
            var result = new Dictionary<string, List<TableStructureInfo>>();
            
            foreach (var table in tables)
            {
                try
                {
                    result[table] = GetTableStructure(_rmsConnectionString, table);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取表 {table} 结构时出错: {ex.Message}");
                    result[table] = new List<TableStructureInfo>();
                }
            }
            
            return result;
        }

        /// <summary>
        /// 获取operatedata数据库中的rmcloseinfo表结构
        /// </summary>
        public List<TableStructureInfo> GetRmCloseInfoStructure()
        {
            try
            {
                return GetTableStructure(_operateDataConnectionString, "rmcloseinfo");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取rmcloseinfo表结构时出错: {ex.Message}");
                return new List<TableStructureInfo>();
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public bool TestConnection(string connectionString, string databaseName)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine($"成功连接到数据库: {databaseName}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"连接数据库 {databaseName} 失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取数据库中的所有表名
        /// </summary>
        public List<string> GetAllTables(string connectionString)
        {
            var result = new List<string>();
            
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    var sql = @"
                        SELECT TABLE_NAME 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_TYPE = 'BASE TABLE'
                        ORDER BY TABLE_NAME";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(reader.GetString("TABLE_NAME"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取表列表时出错: {ex.Message}");
            }
            
            return result;
        }

        /// <summary>
        /// 查看表中的示例数据
        /// </summary>
        public DataTable GetSampleData(string connectionString, string tableName, int topCount = 5)
        {
            var result = new DataTable();
            
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    var sql = $"SELECT TOP {topCount} * FROM [{tableName}]";

                    using (var adapter = new SqlDataAdapter(sql, connection))
                    {
                        adapter.Fill(result);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取表 {tableName} 示例数据时出错: {ex.Message}");
            }
            
            return result;
        }

        /// <summary>
        /// 分析rmcloseinfo表的数据分布
        /// </summary>
        public void AnalyzeRmCloseInfoData()
        {
            try
            {
                using (var connection = new SqlConnection(_operateDataConnectionString))
                {
                    connection.Open();
                    
                    // 分析数据总量
                    var countSql = "SELECT COUNT(*) as TotalCount FROM rmcloseinfo";
                    using (var command = new SqlCommand(countSql, connection))
                    {
                        var totalCount = command.ExecuteScalar();
                        Console.WriteLine($"rmcloseinfo表总记录数: {totalCount}");
                    }
                    
                    // 分析日期范围
                    var dateSql = @"
                        SELECT 
                            MIN(CloseDatetime) as MinDate,
                            MAX(CloseDatetime) as MaxDate
                        FROM rmcloseinfo";
                    using (var command = new SqlCommand(dateSql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Console.WriteLine($"数据日期范围: {reader["MinDate"]} 到 {reader["MaxDate"]}");
                        }
                    }
                    
                    // 分析店铺分布
                    var shopSql = @"
                        SELECT 
                            ShopId,
                            COUNT(*) as RecordCount
                        FROM rmcloseinfo
                        GROUP BY ShopId
                        ORDER BY RecordCount DESC";
                    using (var command = new SqlCommand(shopSql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        Console.WriteLine("店铺数据分布:");
                        while (reader.Read())
                        {
                            Console.WriteLine($"  店铺ID {reader["ShopId"]}: {reader["RecordCount"]} 条记录");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析rmcloseinfo数据时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否存在开台时间字段
        /// </summary>
        public bool CheckOpenTimeField()
        {
            try
            {
                var structure = GetRmCloseInfoStructure();
                var hasOpenTime = structure.Exists(s => 
                    s.ColumnName.ToLower().Contains("open") || 
                    s.ColumnName.ToLower().Contains("start") ||
                    s.ColumnName.ToLower().Contains("begin"));
                
                Console.WriteLine($"rmcloseinfo表是否包含开台时间字段: {hasOpenTime}");
                
                if (!hasOpenTime)
                {
                    Console.WriteLine("建议: 需要添加开台时间字段或从其他表关联获取开台时间");
                }
                
                return hasOpenTime;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查开台时间字段时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成完整的数据库分析报告
        /// </summary>
        public void GenerateAnalysisReport()
        {
            Console.WriteLine("=== KTV业务报告系统 - 数据库分析报告 ===");
            Console.WriteLine();
            
            // 测试连接
            Console.WriteLine("1. 数据库连接测试:");
            TestConnection(_rmsConnectionString, "RMS2019");
            TestConnection(_operateDataConnectionString, "OperateData");
            Console.WriteLine();
            
            // 分析RMS数据库表结构
            Console.WriteLine("2. RMS数据库关键表结构:");
            var rmsStructures = GetRmsTableStructures();
            foreach (var table in rmsStructures)
            {
                Console.WriteLine($"  表: {table.Key}");
                foreach (var column in table.Value)
                {
                    Console.WriteLine($"    {column.ColumnName} ({column.DataType}) {(column.IsNullable ? "NULL" : "NOT NULL")}");
                }
                Console.WriteLine();
            }
            
            // 分析rmcloseinfo表结构
            Console.WriteLine("3. rmcloseinfo表结构:");
            var rmCloseStructure = GetRmCloseInfoStructure();
            foreach (var column in rmCloseStructure)
            {
                Console.WriteLine($"  {column.ColumnName} ({column.DataType}) {(column.IsNullable ? "NULL" : "NOT NULL")}");
            }
            Console.WriteLine();
            
            // 分析数据分布
            Console.WriteLine("4. rmcloseinfo数据分析:");
            AnalyzeRmCloseInfoData();
            Console.WriteLine();
            
            // 检查开台时间字段
            Console.WriteLine("5. 开台时间字段检查:");
            CheckOpenTimeField();
            Console.WriteLine();
            
            Console.WriteLine("=== 分析报告完成 ===");
        }
    }
}

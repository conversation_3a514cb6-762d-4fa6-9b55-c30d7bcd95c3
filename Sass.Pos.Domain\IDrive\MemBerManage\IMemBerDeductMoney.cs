using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.MemBerManage
{
    public interface IMemBerDeductMoney
    {
        ResponseContext<RepDeductMoneyModel> DeductMoney(MemBerDeductMoneyContext context);

        ResponseContext<GetMemBerRuleDataModel> GetMemBerRuleData(GetMemBerRuleDataContext context);
    }
}

using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Common.Extend;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.ServiceClient;
using Saas.Pos.Common.Tools;
using Saas.Pos.Domain.IDrive.MemBerManage;
using Saas.Pos.Drive.DbFood;
using Saas.Pos.Drive.DbFood.Bill;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Drive.MIMS.MemberManage.DeductMoneyMode;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.MIMS.MemberManage
{
    /// <summary>
    /// 会员系统扣款管理
    /// </summary>
    public class MemBerDeductMoneyDrive : MemBerManageSubDriveBase<MemBerManageDriveBase>, IMemBerDeductMoney
    {
        public MemBerDeductMoneyDrive(MemBerManageDriveBase imi, AppSession app)
            : base(imi, app)
        {

        }

        /// <summary>
        /// 查询会员返还规则
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetMemBerRuleDataModel> GetMemBerRuleData(GetMemBerRuleDataContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    if (string.IsNullOrEmpty(context.MemberCardTypeNo))
                        throw new ExMessage("参数不能为空!");

                    //会员返还规则信息
                    var ruleData = app.MIMS.MemBerRule.FindEntity(i => i.MemberCardTypeNo == context.MemberCardTypeNo);
                    if (ruleData == null)
                        throw new ExMessage("未维护该会员等级规则!");

                    var result = ruleData.Clone<GetMemBerRuleDataModel>();

                    return result;

                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "会员系统扣款管理-查询会员返还规则\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                throw new ExMessage("查询会员返还规则失败!" + ex.Message);
            }
        }

        private readonly object _lockObject = new object();
        /// <summary>
        /// 下单扣款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RepDeductMoneyModel> DeductMoney(MemBerDeductMoneyContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.RmNo) || context.UserData.ShopId <= 0)
                        throw new ExMessage("房号和门店ID不能为空!");

                    var result = new RepDeductMoneyModel();
                    BillHandlerDrive billDrive = new BillHandlerDrive();

                    var bill = billDrive.GetActiveBillHead(new GetBillDataContext() { RmNo = context.RmNo, ShopId = context.UserData.ShopId });
                    if (bill.state != ResponseType.success)
                        throw new ExMessage(bill.message);
                    if (bill.data == null)
                        throw new ExMessage("找不到账单信息！");
                    if (bill.data.JZData == null || bill.data.JZData.WaitPay == null)
                        throw new ExMessage("房间状态不可结账!");
                    //应付账单总额
                    if (bill.data.JZData.WaitPay.FullWaitPay <= 0)
                        throw new ExMessage("账单金额为0元，无需结账!");

                    var preCash = 0;
                    var payCode = SaasPos.OrderManage.OrderPayMode.OrderManageGetOrderNoBase.GetCode();
                    //该代码块的顺序不能在CashFee的后面
                    if (context.PreRecord != null && context.PreRecord.CashData != null)
                    {
                        context.PreRecord.CashData.ForEach(cash =>
                        {
                            if (cash.Inputs.Any(w => w.Alias == "Cash") && !string.IsNullOrEmpty(cash.Inputs.FirstOrDefault(w => w.Alias == "Cash").Value))
                                preCash += int.Parse(cash.Inputs.FirstOrDefault(w => w.Alias == "Cash").Value);
                            if (cash.Inputs.Any(w => w.Alias == "HKCash") && !string.IsNullOrEmpty(cash.Inputs.FirstOrDefault(w => w.Alias == "HKCash").Value))
                                preCash += int.Parse(cash.Inputs.FirstOrDefault(w => w.Alias == "HKCash").Value);
                            if (cash.Inputs.Any(w => w.Alias == "HKCardCash") && !string.IsNullOrEmpty(cash.Inputs.FirstOrDefault(w => w.Alias == "HKCardCash").Value))
                                preCash += int.Parse(cash.Inputs.FirstOrDefault(w => w.Alias == "HKCardCash").Value);
                            if (cash.Inputs.Any(w => w.Alias == "CardCash") && !string.IsNullOrEmpty(cash.Inputs.FirstOrDefault(w => w.Alias == "CardCash").Value))
                                preCash += int.Parse(cash.Inputs.FirstOrDefault(w => w.Alias == "CardCash").Value);
                        });

                        var preContext = new SavePreRecordContext()
                        {
                            ShopId = context.UserData.ShopId,
                            InvNo = bill.data.Invno,
                            RmNo = context.RmNo,
                            PreData = context.PreRecord,
                            TradeCode = payCode,
                            UserData = new Model.General.UserOperatorModel()
                            {
                                UserId = context.UserData.UserId
                            }
                        };

                        var resp = billDrive.BillPaymentRecord.SavePreRecord(preContext);
                        if (resp.state != ResponseType.success || !resp.data.Boole)
                            LogHelper.Error($"保存订单预付信息失败！{resp.message}，请求参数:{context.PreRecord.ToJson()}");
                    }

                    if (context.CashFee > 0 || preCash > 0)
                    {
                        if (bill.data.JZData.WaitPay.FullWaitPay < ((context.CashFee / 100) + preCash) + context.AccountUse.RechargeMonry + context.AccountUse.ReturnMoney)
                            throw new ExMessage("现金支付金额超过全单应付金额！");

                        var posClient = ServiceClientBase.GetPosClient(context.UserData.ShopId);

                        var resp = posClient.Bill_AddWxPayInfo(new SERVICE.PROXY.PosService.Bill_AddWxPayInfoContext()
                        {
                            ShopId = context.UserData.ShopId,
                            RmNo = context.RmNo,
                            InvNo = bill.data.Invno,
                            Tol = context.CashFee + (preCash * 100),
                            transaction_id = payCode,
                            out_trade_no = payCode,
                            WxPay = 0,
                            PayName = 0,
                            RechargeMoney = 0,
                            CouponFee = 0,
                            GroupAmount = 0,
                            PayType = 5,//现金支付
                            th_minus_fee = 0,
                        });

                        if (resp.state != ResponseType.success)
                            LogHelper.Error("现金支付插入微信支付记录失败：" + resp.message);

                        result.IsSucces = resp.data;
                    }

                    if (context.MemberKey != null && context.MemberKey != Guid.Empty && (context.AccountUse.RechargeMonry + context.AccountUse.ReturnMoney) > 0)
                    {
                        //充值账户:落单、房费
                        if (context.AccountUse.RechargeMonry + context.AccountUse.ReturnMoney + preCash + (context.CashFee / 100) > bill.data.JZData.WaitPay.FullWaitPay)
                            throw new ExMessage("输入的充值金额加房费金额已超出全单金额!");
                        //房费账户:房费
                        if (context.AccountUse.ReturnMoney > bill.data.JZData.WaitPay.FullRoomWaitPay)
                            throw new ExMessage("使用房费金额大于应付房费金额!");

                        MemBerManageDrive mainDrive = new MemBerManageDrive();
                        #region 获取会员信息与校验会员账户状态
                        //会员信息
                        var memberData = app.MIMS.MemberInfo.FindEntity(i => i.MemberKey == context.MemberKey);
                        if (memberData == null)
                            throw new ExMessage("当前用户不是会员!");
                        //会员卡状态信息
                        var cardData = app.MIMS.MemberCardInfo.IQueryable(i => i.MemberKey == memberData.MemberKey)
                    .OrderByDescending(i => i.MemberCardRegDate).FirstOrDefault();
                        if (cardData == null)
                            throw new ExMessage("当前用户不是会员!");
                        if (cardData.MemberCardStatus == 5)
                            throw new ExMessage("会员卡已冻结！");

                        //判断会员余额
                        if (context.AccountUse.RechargeMonry > memberData.RechargeTotal || context.AccountUse.ReturnMoney > memberData.ReturnTotal)
                            throw new ExMessage("会员账户余额不足以结账!");

                        var cardStateData = mainDrive.Mbc.GetMemBerCardStateData(new RelieveMemBerRiskControlExContext()
                        {
                            MemBerKey = memberData.MemberKey,
                            MemberData = memberData,
                            MemberCardData = cardData
                        });
                        if (cardStateData.IsLoss)
                            throw new ExMessage("会员卡已挂失！");
                        if (!cardStateData.IsAccredit)
                            throw new ExMessage("会员未授权或授权过期！");
                        if (!cardStateData.IsAccountState)
                            throw new ExMessage("账户状态不可兑换!");

                        //检测风控
                        if (mainDrive.Mbr.MemBerRiskControl(new MemBerRiskControlContext() { MemBerKey = context.MemberKey, ShopId = context.UserData.ShopId, MemberData = memberData, CardData = cardData }))
                            throw new ExMessage("账户被冻结!");

                        #endregion

                        MemberInfo memberInfo = null;
                        MemBerRule memBerRule = null;
                        lock (_lockObject)
                        {
                            #region 扣款

                            var deEx = context.Clone<DeductMoneyContextEx>();
                            deEx.InvNo = bill.data.Invno;

                            //账户扣款数据
                            var acc = mainDrive.MemBerDeductMoney(deEx);
                            if ((acc.rechargeInfos == null || acc.rechargeInfos.Count <= 0) && (acc.returnInfos == null || acc.returnInfos.Count <= 0))
                                throw new ExMessage("计算会员扣款错误！");
                            if (memberData.RechargeTotal < context.AccountUse.RechargeMonry || memberData.ReturnTotal < context.AccountUse.ReturnMoney)
                                throw new ExMessage("会员账户余额不足以结账!");

                            //记录扣款记录
                            acc.MemberRecord = new MemBerSccountRecord()
                            {
                                ShopId = context.UserData.ShopId,
                                InvNo = bill.data.Invno,
                                MemBerKey = context.MemberKey,
                                AccountDatetime = DateTime.Now
                            };

                            var count = app.MIMS.MemberInfo.MemBerDeductMoney(acc);
                            if (count <= 1)
                            {
                                LogHelper.Error("会员扣款失败，相关参数：" + context.ToJson());
                                throw new Exception("会员扣款失败！");
                            }

                            result.IsSucces = true;
                            //重新获取会员卡信息
                            memberInfo = app.MIMS.MemberInfo.FindEntity(w => w.MemberKey == memberData.MemberKey);
                            //查询房费返还规则
                            memBerRule = app.MIMS.MemBerRule.FindEntity(w => w.ShopId == context.UserData.ShopId && w.MemberCardTypeNo == memberData.MemberCardTypeNo);

                            var shopInfo = app.SaasPos.Shop.FindEntity(w => w.ShopId == context.UserData.ShopId);
                            if (shopInfo != null)
                                bill.data.ShopName = $"（{shopInfo.BrandName}）{shopInfo.ShopName}";

                            //发送扣款短信
                            var msg = PhoneMessageHelper.MemberDeducty(new MemberDeductyModel()
                            {
                                MemberCardNo = cardData.MemberCardNumber,
                                MemberCardTypeName = Enum.GetName(typeof(MemBerCardTypeNoEnum), Convert.ToInt32(memberData.MemberCardTypeNo)),
                                ShopName = bill.data.ShopName,
                                MemberName = memberData.MemberName,
                                RechargeTot = memberInfo.RechargeTotal,
                                ReturnTot = memberInfo.ReturnTotal,
                                RechargeUseTot = context.AccountUse.RechargeMonry,
                                ReturnUseTot = context.AccountUse.ReturnMoney,
                                Tot = bill.data.JZData.Tot,
                            });

                            var sendOk = MessageSendHelper.PhoneSendMessage(new PhoneSendMessageContext()
                            {
                                ReqType = 1,
                                No = context.UserData.ShopId == 11 ? 1 : 0,
                                Message = msg,
                                PhoneNumber = memberInfo.MemberPhoneNumber,
                                Title = "1"
                            });
                            if (!sendOk)
                                LogHelper.Error($"会员卡：{cardData.MemberCardNumber}，短信发送失败！");

                            #endregion
                        }

                        DbFood.MemberInfoDrive infoDrive = new DbFood.MemberInfoDrive();

                        if (result.IsSucces)
                        {
                            try
                            {
                                var returnValue = 0;
                                if (context.AccountUse.ReturnMoney <= 0)
                                    returnValue = (int)Math.Floor((double)(bill.data.JZData.Tot * memBerRule.ReturnScale) / 100);

                                var chechData = new AddMemberChechOutInfoContext()
                                {
                                    InvNo = bill.data.Invno,
                                    MemberKey = context.MemberKey.ToString(),
                                    RmCost = bill.data.JZData.RmCost,
                                    RmNo = context.RmNo,
                                    ShopId = context.UserData.ShopId,
                                    MemberNo = cardData.MemberCardNumber,
                                    IntegralVal = 0,
                                    RechargePayTot = context.AccountUse.RechargeMonry,
                                    ReturnPayTot = context.AccountUse.ReturnMoney,
                                    BillDisVal = bill.data.JZData.WaitPay.FullWaitPay - context.AccountUse.RechargeMonry - context.AccountUse.ReturnMoney,//账单剩余待支付金额
                                    MemberDeduction = new AddMemberDeductionContext()
                                    {
                                        MemberCardTypeName = Enum.GetName(typeof(MemBerCardTypeNoEnum), Convert.ToInt32(memberData.MemberCardTypeNo)),
                                        CheckUserName = context.UserData.UserName,
                                        MemberName = memberData.MemberName,
                                        MemberPhone = memberData.MemberPhoneNumber,
                                        RechargePay = context.AccountUse.RechargeMonry,
                                        ReturnPay = context.AccountUse.ReturnMoney,
                                        RechargeTot = memberInfo.RechargeTotal,
                                        ReturnTot = memberInfo.ReturnTotal,
                                        Tot = bill.data.JZData?.WaitPay?.FullWaitPay ?? 0,
                                        ReturnValue = returnValue,
                                        CashPay = (context.CashFee / 100) + preCash,
                                    }
                                };

                                //记录房间消费信息
                                var chechCount = infoDrive.AddDbMemberChechOutData(chechData);
                                if (chechCount.state != ResponseType.success || chechCount.data == null)
                                {
                                    result.IsSucces = false;
                                    throw new ExMessage(chechCount.message);
                                }
                            }
                            catch (Exception ex)
                            {
                                throw new ExMessage("扣款成功但记录消费失败!" + ex.Message);
                            }
                        }
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    LogHelper.Error(DateTime.Now + "报错:会员系统扣款管理-下单扣款\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage("结账失败!" + ex.Message);
                }
            });
        }
    }
}
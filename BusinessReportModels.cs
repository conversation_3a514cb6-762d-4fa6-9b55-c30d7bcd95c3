using System;
using System.Collections.Generic;

namespace Sass.Pos.BusinessReport
{
    /// <summary>
    /// 商店业务信息
    /// </summary>
    public class ShopBusinessInfo
    {
        public int ShopId { get; set; }
        public string ShopName { get; set; }
        public DateTime BusinessDate { get; set; }
    }

    /// <summary>
    /// 消费时间戳信息
    /// </summary>
    public class ConsumptionTimeInfo
    {
        public string InvNo { get; set; }
        public DateTime? OpenDateTime { get; set; }  // 房间开放时间
        public DateTime CloseDateTime { get; set; }   // 结账时间
        public int ShopId { get; set; }
        public decimal TotalAmount { get; set; }      // 总消费金额
    }

    /// <summary>
    /// 渠道信息
    /// </summary>
    public class ChannelInfo
    {
        public string InvNo { get; set; }
        public string ChannelType { get; set; }  // meituan, douyin, vip_reserved, room_fee, K+
        public int ShopId { get; set; }
    }

    /// <summary>
    /// 时间段配置信息
    /// </summary>
    public class TimeSlotConfig
    {
        public string TimeNo { get; set; }
        public string TimeName { get; set; }
        public int BegTime { get; set; }    // 开始时间（分钟）
        public int EndTime { get; set; }    // 结束时间（分钟）
        public int ShopId { get; set; }
        
        /// <summary>
        /// 获取格式化的时间段显示
        /// </summary>
        public string TimeSlotDisplay => $"{FormatTime(BegTime)}-{FormatTime(EndTime)}";
        
        private string FormatTime(int minutes)
        {
            var hours = minutes / 60;
            var mins = minutes % 60;
            return $"{hours:D2}:{mins:D2}";
        }
    }

    /// <summary>
    /// 时间段重叠检测结果
    /// </summary>
    public class TimeSlotOverlapResult
    {
        public string InvNo { get; set; }
        public string TimeSlotNo { get; set; }
        public string TimeSlotName { get; set; }
        public TimeSpan ConsumptionStart { get; set; }
        public TimeSpan ConsumptionEnd { get; set; }
        public TimeSpan SlotStart { get; set; }
        public TimeSpan SlotEnd { get; set; }
        public decimal TotalAmount { get; set; }
    }

    /// <summary>
    /// 渠道分类结果
    /// </summary>
    public class ChannelClassificationResult
    {
        public string InvNo { get; set; }
        public string TimeSlotNo { get; set; }
        public string TimeSlotName { get; set; }
        public string ChannelType { get; set; }
        public decimal TotalAmount { get; set; }
    }

    /// <summary>
    /// 前一插槽延续结果
    /// </summary>
    public class PreviousSlotContinuationResult
    {
        public string TimeSlotNo { get; set; }
        public string TimeSlotName { get; set; }
        public int ContinuationCount { get; set; }
    }

    /// <summary>
    /// 业务报告
    /// </summary>
    public class BusinessReport
    {
        public int ShopId { get; set; }
        public string ShopName { get; set; }
        public DateTime BusinessDate { get; set; }
        public DateTime GeneratedTime { get; set; }
        
        public List<TimeSlotConfig> TimeSlots { get; set; } = new List<TimeSlotConfig>();
        public List<ChannelClassificationResult> ChannelResults { get; set; } = new List<ChannelClassificationResult>();
        public List<PreviousSlotContinuationResult> ContinuationResults { get; set; } = new List<PreviousSlotContinuationResult>();
        
        /// <summary>
        /// 获取报告矩阵数据（时间插槽为列，渠道为行）
        /// </summary>
        public ReportMatrix GetReportMatrix()
        {
            var matrix = new ReportMatrix();
            
            // 设置列标题（时间段）
            matrix.TimeSlotHeaders = TimeSlots.Select(t => new TimeSlotHeader
            {
                TimeNo = t.TimeNo,
                TimeName = t.TimeName,
                TimeDisplay = t.TimeSlotDisplay
            }).ToList();
            
            // 获取所有渠道类型
            var channelTypes = ChannelResults.Select(c => c.ChannelType).Distinct().ToList();
            
            // 为每个渠道创建行数据
            foreach (var channelType in channelTypes)
            {
                var row = new ReportRow
                {
                    ChannelType = channelType,
                    TimeSlotData = new Dictionary<string, ReportCellData>()
                };
                
                foreach (var timeSlot in TimeSlots)
                {
                    var cellData = ChannelResults
                        .Where(c => c.ChannelType == channelType && c.TimeSlotNo == timeSlot.TimeNo)
                        .ToList();
                    
                    row.TimeSlotData[timeSlot.TimeNo] = new ReportCellData
                    {
                        Count = cellData.Count,
                        TotalAmount = cellData.Sum(c => c.TotalAmount),
                        InvNos = cellData.Select(c => c.InvNo).ToList()
                    };
                }
                
                matrix.Rows.Add(row);
            }
            
            // 添加前一插槽延续数据行
            var continuationRow = new ReportRow
            {
                ChannelType = "前一插槽延续",
                TimeSlotData = new Dictionary<string, ReportCellData>()
            };
            
            foreach (var timeSlot in TimeSlots)
            {
                var continuation = ContinuationResults.FirstOrDefault(c => c.TimeSlotNo == timeSlot.TimeNo);
                continuationRow.TimeSlotData[timeSlot.TimeNo] = new ReportCellData
                {
                    Count = continuation?.ContinuationCount ?? 0,
                    TotalAmount = 0,
                    InvNos = new List<string>()
                };
            }
            
            matrix.Rows.Add(continuationRow);
            
            return matrix;
        }
    }

    /// <summary>
    /// 报告矩阵
    /// </summary>
    public class ReportMatrix
    {
        public List<TimeSlotHeader> TimeSlotHeaders { get; set; } = new List<TimeSlotHeader>();
        public List<ReportRow> Rows { get; set; } = new List<ReportRow>();
    }

    /// <summary>
    /// 时间段标题
    /// </summary>
    public class TimeSlotHeader
    {
        public string TimeNo { get; set; }
        public string TimeName { get; set; }
        public string TimeDisplay { get; set; }
    }

    /// <summary>
    /// 报告行数据
    /// </summary>
    public class ReportRow
    {
        public string ChannelType { get; set; }
        public Dictionary<string, ReportCellData> TimeSlotData { get; set; } = new Dictionary<string, ReportCellData>();
    }

    /// <summary>
    /// 报告单元格数据
    /// </summary>
    public class ReportCellData
    {
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public List<string> InvNos { get; set; } = new List<string>();
    }

    /// <summary>
    /// 数据库表结构查询结果
    /// </summary>
    public class TableStructureInfo
    {
        public string TableName { get; set; }
        public string ColumnName { get; set; }
        public string DataType { get; set; }
        public bool IsNullable { get; set; }
        public string DefaultValue { get; set; }
    }

    /// <summary>
    /// rmcloseinfo表的完整结构映射
    /// </summary>
    public class RmCloseInfoComplete
    {
        public string InvNo { get; set; }
        public int Cash { get; set; }
        public int Cash_Targ { get; set; }
        public int Vesa { get; set; }
        public string VesaName { get; set; }
        public string VesaNo { get; set; }
        public int Vesa_Targ { get; set; }
        public string VesaName_Targ { get; set; }
        public string VesaNo_Targ { get; set; }
        public int GZ { get; set; }
        public string GZName { get; set; }
        public int AccOkZD { get; set; }
        public string ZDName { get; set; }
        public int NoPayed { get; set; }
        public string NoPayedName { get; set; }
        public int Check { get; set; }
        public string CheckName { get; set; }
        public int WXPay { get; set; }
        public string OpenId { get; set; }
        public string wx_out_trade_no { get; set; }
        public int AliPay { get; set; }
        public string user_id { get; set; }
        public string Ali_out_trade_no { get; set; }
        public int MTPay { get; set; }
        public string MTPayNo { get; set; }
        public int DZPay { get; set; }
        public string DZPayNo { get; set; }
        public int NMPay { get; set; }
        public string NMPayNo { get; set; }
        public int Coupon { get; set; }
        public string CouponName { get; set; }
        public int RechargeAccount { get; set; }
        public string RechargeMemberCardNo { get; set; }
        public int ReturnAccount { get; set; }
        public string ReturnMemberCardNo { get; set; }
        public DateTime CloseDatetime { get; set; }
        public Guid MemberKey { get; set; }
        public string CloseUserName { get; set; }
        public string CloseUserId { get; set; }
        public int ShopId { get; set; }
        public string RmNo { get; set; }
        public DateTime? OpenDateTime { get; set; }  // 需要添加的开台时间字段
        
        /// <summary>
        /// 计算总金额
        /// </summary>
        public decimal TotalAmount => Cash + Vesa + WXPay + AliPay + MTPay + DZPay + NMPay + Coupon + RechargeAccount;
        
        /// <summary>
        /// 获取主要支付渠道
        /// </summary>
        public string GetPrimaryChannel()
        {
            if (MTPay > 0) return "meituan";
            if (DZPay > 0) return "douyin";
            if (WXPay > 0 || AliPay > 0) return "online_payment";
            if (Vesa > 0) return "vip_reserved";
            if (Cash > 0) return "room_fee";
            return "K+";
        }
    }
}

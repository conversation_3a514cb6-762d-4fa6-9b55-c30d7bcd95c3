using Saas.Pos.Domain.IRepository.MIMS;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.MIMS;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;

using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace Saas.Pos.Repository.Lib.MIMS
{
    public partial class MemberInfoRepository : BaseRepository<MemberInfo>, IMemberInfoRepository
    {
        /// <summary>
        /// 下单扣款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public int MemBerDeductMoney(MemBerAcc context)
        {
            var nowDate = DateTime.Now;
            StringBuilder sb = new StringBuilder();
            sb.Append(@" BEGIN TRY
                    BEGIN TRANSACTION; -- 开始事务
                    ");
            //var resCount = 0;
            //resCount = context.integralInfos.Count() + context.pointInfos.Count() + context.rechargeInfos.Count() + context.returnInfos.Count();

            //扣款记录
            if (context.MemberRecord != null)
                sb.Append(string.Format("insert into MemBerSccountRecord(InvNo,MemBerKey,ShopId,AccountDatetime,IsRisk) values('{0}'," +
                    "'{1}',{2},'{3}',{4});", context.MemberRecord.InvNo, context.MemberRecord.MemBerKey, context.MemberRecord.ShopId,
                    context.MemberRecord.AccountDatetime, context.MemberRecord.IsRisk ? 1 : 0));

            foreach (var item in context.integralInfos)
            {
                sb.Append(string.Format("insert into IntegralInfo(IntegralDate,IntegralKey,IntegralRmNo,IntegralRmTotal,InvNo,IntegralShopId," +
                    "IntegralType,IntegralUserName,IntegralValue,MemberKey,Val3,Val6,Val4) values('{0}','{1}','{2}',{3},'{4}',{5},{6},'{7}'," +
                    "{8},'{9}','{10}','{11}','{12}');", nowDate, Guid.NewGuid(), item.IntegralRmNo, item.IntegralRmTotal, item.InvNo, item.IntegralShopId,
                    item.IntegralType, item.IntegralUserName, item.IntegralValue, item.MemberKey, string.Empty, string.Empty, string.Empty));
            }

            foreach (var item in context.pointInfos)
            {
                sb.Append(string.Format("insert into PointInfo(MemberKey,InvNo,PointDate,PointKey,PointRmNo,PointShopId," +
                    "PointType,PointUserName,PointValue,Val3,Val6,Val4) values('{0}','{1}','{2}','{3}','{4}',{5},{6},'{7}'," +
                    "{8},'{9}','{10}','{11}');", item.MemberKey, item.InvNo, nowDate, item.PointKey, item.PointRmNo, item.PointShopId,
                    item.PointType, item.PointUserName, item.PointValue, item.Val3, item.Val6, item.Val4));
            }

            foreach (var item in context.returnInfos)
            {
                sb.Append(string.Format("insert into ReturnInfo(InvNo,MemberKey,ReturnDate,ReturnKey,ReturnRmNo,ReturnShopId," +
                    "ReturnType,ReturnUserName,ReturnValue,Val3,Val6,Val4) values('{0}','{1}','{2}','{3}','{4}',{5},{6},'{7}'," +
                    "{8},'{9}','{10}','{11}');", item.InvNo, item.MemberKey, nowDate, item.ReturnKey, item.ReturnRmNo, item.ReturnShopId,
                    item.ReturnType, item.ReturnUserName, item.ReturnValue, item.Val3, item.Val6, item.Val4));
            }

            foreach (var item in context.rechargeInfos)
            {
                sb.Append(string.Format("insert into RechargeInfo(InvNo,MemberKey,RechargeCheckUserName,RechargeUserName,RechargeDate,RechargeKey," +
                    "RechargeRmNo,RechargeShopId,RechargeType,RechargeValue,Val3,Val6,Val4) values('{0}','{1}','{2}','{3}','{4}','{5}','{6}',{7}," +
                    "{8},{9},'{10}','{11}','{12}');", item.InvNo, item.MemberKey, item.RechargeCheckUserName, item.RechargeUserName, nowDate
                    , item.RechargeKey, item.RechargeRmNo, item.RechargeShopId, item.RechargeType, item.RechargeValue, item.Val3, item.Val6, item.Val4));
            }
            //var sp1 = new SqlParameter("@sqlStatement", sb.ToString());
            //var sp2 = new SqlParameter
            //{
            //    ParameterName = "@rowCountEx",
            //    SqlDbType = SqlDbType.Int,
            //    Direction = ParameterDirection.Output
            //};
            //var parameters = new List<SqlParameter>()
            //{
            //    sp1,
            //    sp2
            //}.ToArray();
            sb.Append(@"COMMIT TRANSACTION; -- 提交事务
                        END TRY
                        BEGIN CATCH
                            ROLLBACK TRANSACTION; -- 若出现异常则回滚事务
                            THROW;
                        END CATCH");
            //db.Database.ExecuteSqlCommand("EXEC MemBerDeductMoney @sqlStatement, @rowCountEx OUTPUT", parameters);
            var count = db.Database.ExecuteSqlCommand(sb.ToString());
            if (count <= 0)
                throw new ExMessage("扣款失败!");
            return count;
        }

        /// <summary>
        /// 获取在线预订每日新关数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetBookingNewAttDayDataModel> GetBookingNewAttDayData(GetBookingNewAttDayDataContext context)
        {
            var sql = $@"SELECT * from (
                        SELECT
                            max(a.inTime) as inTime,
	                        a.shopid,
	                        b.ShopName,
	                        COUNT ( 1 ) as Num
                        FROM
	                        Wxinfo.dbo.onlinebookfocus a
	                        JOIN MIMS.dbo.ShopInfo b on a.shopid = b.ShopId
                        WHERE
	                        a.shopid > 0 
	                        AND a.inTime BETWEEN '{context.StartDate.Date}' 
	                        AND '{context.EndDate.Date}' 
                        GROUP BY
                        CONVERT(DATE, a.inTime, 120),
	                        a.shopid,
	                        b.ShopName)t ORDER BY t.inTime,t.shopid";

            var list = db.Database.SqlQuery<GetBookingNewAttDayDataModel>(sql).ToList();

            context.Pagination.records = list.Count();
            var data = list.OrderBy(i => i.inTime).OrderBy(i => i.ShopId)
                           .Skip(context.Pagination.rows * (context.Pagination.page - 1))
                           .Take(context.Pagination.rows).ToList();
            return data;
        }

        /// <summary>
        /// 导出获取在线预订每日新关数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<ExportBookingData> ExportGetBookingNewAttDayData(ExportBookingNewAttDayDataContext context)
        {
            var sql = $@"SELECT * from (
                        SELECT
                        CONVERT(DATE, a.inTime, 120) as inTime,
	                        a.shopid,
	                        b.ShopName,
	                        COUNT ( 1 ) as Num
                        FROM
	                        Wxinfo.dbo.onlinebookfocus a
	                        JOIN MIMS.dbo.ShopInfo b on a.shopid = b.ShopId
                        WHERE
	                        a.shopid > 0 
	                        AND a.inTime BETWEEN '{context.StartDate.Date}' 
	                        AND '{context.EndDate.Date}' 
                        GROUP BY
                        CONVERT(DATE, a.inTime, 120),
	                        a.shopid,
	                        b.ShopName)t ORDER BY t.inTime,t.shopid";

            var list = db.Database.SqlQuery<GetBookingNewAttDayDataModel>(sql).ToList();

            var data = list.GroupBy(i => i.inTime).Select(i => new ExportBookingData()
            {
                inTime = i.Key,
                ShopDatas = i.Select(j => new ShopData()
                {
                    Num = j.Num,
                    ShopId = j.ShopId,
                    ShopName = j.ShopName
                }).ToList()
            }).ToList();

            return data;
        }

        /// <summary>
        /// 调用提成绑定存储过程
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public MemberTopUpEditModel MemberTopUpEdit(MemberTopUpEditContext context)
        {
            var invNoParam = new SqlParameter("@InvNo", context.InvNo); // 以此类推，替换参数
            var shopIdParam = new SqlParameter("@RechargeShopid", context.RechargeShopId);
            var userNameParam = new SqlParameter("@UserName", context.UserName);
            var transactionIdParam = new SqlParameter("@transaction_id", context.TransactionId);

            var result = db.Database.SqlQuery<MemberTopUpEditModel>(
                "EXEC BindingRechargeBonus @InvNo, @RechargeShopid, @UserName, @transaction_id",
                invNoParam, shopIdParam, userNameParam, transactionIdParam
            ).FirstOrDefault();

            return result;
        }

        /// <summary>
        /// 查询会员卡开卡记录报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetMemberCardRecordDataModel> GetMemberCardRecordData(GetMemberCardRecordDataContext context)
        {
            var cardDate = context.CardTime.AddDays(1);

            var shop = (from s in db.ShopInfo
                        where s.IsMemberCard == true
                        select new
                        {
                            s.ShopID,
                            s.ShopName
                        }).ToList();

            var orderCard = (from ccr in db.MemberCardOrderRecord
                             where ccr.CreateTime >= context.CardTime.Date && ccr.CreateTime <= cardDate
                             select new
                             {
                                 ccr.MemberCardTypeNo,
                                 ccr.ShopId,
                                 ccr.MemberCardSoure,
                                 ccr.RoyaltyShopId,
                                 ccr.CreateTime,
                                 ccr.MenberCardPrice
                             }).ToList();

            var cardData = (from mc in db.MemberCardInfo
                            where mc.MemberCardRegDate >= context.CardTime.Date && mc.MemberCardRegDate <= cardDate
                            select new
                            {
                                mc.ShopId,
                                mc.MemberCardRegDate,
                                mc.MemberCardTypeNo
                            }).ToList();

            // 使用 SelectMany 拼接 shop 集合和 会员卡等级 集合
            var result = shop.SelectMany(a => new MemberCardTypeModel().EnumList, (a, b) => new
            {
                ShopId = a.ShopID,
                ShopName = a.ShopName,
                MemberCardTypeNo = b.Key,
                MemberCardTypeName = b.Value
            }).ToList();

            var data = new List<GetMemberCardRecordDataModel>();

            var memberNum = 0;
            var twNum = 0;
            var mtNum = 0;
            var thNum = 0;
            foreach (var item in result)
            {
                if (item.ShopId <= 0)
                    //办卡门店为0，提成门店为空是微信支付
                    memberNum = orderCard.Where(i => i.ShopId <= 0 &&
                    (i.RoyaltyShopId == null || i.RoyaltyShopId <= 0) && i.ShopId == item.ShopId && i.MemberCardTypeNo
                            == item.MemberCardTypeNo).Count();
                else
                    memberNum = orderCard.Where(i => i.ShopId == item.ShopId && i.MemberCardTypeNo
                            == item.MemberCardTypeNo).Count();

                //办卡门店为0，提成门店不为空是公众号
                twNum = orderCard.Where(i => i.ShopId <= 0 &&
                (i.RoyaltyShopId != null || i.RoyaltyShopId > 0) && i.RoyaltyShopId == item.ShopId && i.MemberCardTypeNo
                        == item.MemberCardTypeNo && i.MemberCardSoure == 1).Count();
                mtNum = orderCard.Where(i => i.ShopId <= 0 &&
                (i.RoyaltyShopId != null || i.RoyaltyShopId > 0) && i.RoyaltyShopId == item.ShopId && i.MemberCardTypeNo
                      == item.MemberCardTypeNo && i.MemberCardSoure == 2).Count();
                thNum = orderCard.Where(i => i.ShopId <= 0 &&
                (i.RoyaltyShopId != null || i.RoyaltyShopId > 0) && i.RoyaltyShopId == item.ShopId && i.MemberCardTypeNo
                        == item.MemberCardTypeNo && i.MemberCardSoure == 3).Count();

                data.Add(new GetMemberCardRecordDataModel()
                {
                    ShopId = item.ShopId,
                    ShopName = item.ShopName,
                    MemberCardTypeName = item.MemberCardTypeName,
                    MemberCardTypeNo = item.MemberCardTypeNo,
                    MemberNum = memberNum,
                    TwNum = twNum,
                    MtNum = mtNum,
                    ThNum = thNum,
                    DiffNum = memberNum - cardData.Where(i => i.ShopId == item.ShopId && i.MemberCardTypeNo == item.MemberCardTypeNo).Count()
                });
            }

            return data.OrderByDescending(i => i.ShopId).ToList();
        }

        /// <summary>
        /// 发送审核
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public bool IntegralOreder(IntegralOrederContext context)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("exec Hd_Public.dbo.ConfirmData @t=@t,@WebRequestKey=@WebRequestKey,@ShopId=@ShopId," +
                "@RmNo=@RmNo,@Price=@Price,@Integral=@Integral,@OpUserId=@OpUserId" +
                ",@OpUserName=@OpUserName,@ChUserId=@ChUserId,@ChUserName=@ChUserName,@MemberKey=@MemberKey" +
                ",@DataSource=@DataSource,@InBillName=@InBillName");

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@t","2"),
                new SqlParameter("@WebRequestKey",context.WebRequestKey),
                new SqlParameter("@ShopId",context.ShopId),
                new SqlParameter("@RmNo",context.RmNo),
                new SqlParameter("@Price",context.orderList.Select(i=>i.FdPrice*i.FdQty).Sum()),
                new SqlParameter("@Integral",context.Intergral),
                new SqlParameter("@OpUserId",context.OpUserId),
                new SqlParameter("@OpUserName",context.OpUserId),
                new SqlParameter("@ChUserId",context.ChUserId),
                new SqlParameter("@ChUserName",context.ChUserId),
                new SqlParameter("@MemberKey",Guid.Parse(context.MemberKey)),
                new SqlParameter("@DataSource",context.DataSource),
                new SqlParameter("@InBillName",context.InBillName),
            }.ToArray();

            return db.Database.ExecuteSqlCommand(sb.ToString(), parameters) > 0;

        }

        public BindMemberInfo GetBindMemberInfo(string cardNumber)
        {
            var data = (from member in db.MemberInfo
                        join cardInfo in db.MemberCardInfo
                        on member.MemberKey equals cardInfo.MemberKey
                        where member.Val4 == cardNumber || cardInfo.MemberCardNumber == cardNumber
                        select new BindMemberInfo()
                        {
                            MemberCardNumber = member.Val4 == "" ? cardInfo.MemberCardNumber : member.Val4,
                            IntegralTotal = member.IntegralTotal,
                            RechargeTotal = member.RechargeTotal,
                            PointTotal = member.PointTotal,
                            ReturnTotal = member.ReturnTotal
                        }).FirstOrDefault();

            return data;
        }

        public int InsertMember(MemberInfo member)
        {
            var insertSql = @"INSERT INTO [dbo].[MemberInfo] ([MemberKey], [RegShopId], [MemberRegDate], [MemberName], [MemberSex], [MemberBirthday], [MemberIDNumber], [MemberPhoneNumber], [MemberAddress], [MemberCardTypeNo], [PointTotal], [IntegralTotal], [RechargeTotal], [ReturnTotal], [RegUserName], [UpdateUserName], [UpdateDate], [Val1], [Val2], [Val3], [Val4], [Val6], [MemberPwd], [MemberCardRelationName], [RechargeTotalFrozen]) VALUES (@MemberKey, @RegShopId, @MemberRegDate, @MemberName, @MemberSex,@MemberBirthday, @MemberIDNumber, @MemberPhoneNumber, @MemberAddress, @MemberCardTypeNo, 0, 0, 0, 0, @RegUserName, '', '', 0, 0, @OpenId, '', '', NULL, '', 0);";

            var parmarter = new List<SqlParameter>()
            {
                new SqlParameter("@MemberKey",member.MemberKey),
                new SqlParameter("@RegShopId",member.RegShopId),
                new SqlParameter("@MemberRegDate",member.MemberRegDate),
                new SqlParameter("@MemberName",member.MemberName),
                new SqlParameter("@MemberSex",member.MemberSex),
                new SqlParameter("@MemberBirthday",member.MemberBirthday),
                new SqlParameter("@MemberIDNumber",member.MemberIDNumber),
                new SqlParameter("@MemberPhoneNumber",member.MemberPhoneNumber),
                new SqlParameter("@MemberAddress",member.MemberAddress),
                new SqlParameter("@MemberCardTypeNo",member.MemberCardTypeNo),
                new SqlParameter("@RegUserName",member.RegUserName),
                new SqlParameter("@OpenId",member.Val3),
            };

            return db.Database.ExecuteSqlCommand(insertSql, parmarter.ToArray());
        }

        public int InsertMemberCard(MemberCardInfo cardInfo)
        {
            var insertSql = @"INSERT INTO [dbo].[MemberCardInfo] ([MemberCardKey], [ShopId], [MemberCardTypeNo], [MemberKey], [MemberCardNumber], [MemberCardFixNumber], [MemberCardRegDate], [MemberCardCloseDate], [MemberCardStatus], [MemberCardRelationName], [Val1], [Val2], [Val3], [Val4], [Val6]) VALUES (@MemberCardKey, @ShopId, @MemberCardTypeNo, @MemberKey, @MemberCardNumber,@MemberCardFixNumber, @MemberCardRegDate, NULL, @MemberCardStatus, '', 0, 0, '', '', '');";

            var parmarter = new List<SqlParameter>()
            {
                new SqlParameter("@MemberCardKey",cardInfo.MemberCardKey),
                new SqlParameter("@ShopId",cardInfo.ShopId),
                new SqlParameter("@MemberCardTypeNo",cardInfo.MemberCardTypeNo),
                new SqlParameter("@MemberKey",cardInfo.MemberKey),
                new SqlParameter("@MemberCardNumber",cardInfo.MemberCardNumber),
                new SqlParameter("@MemberCardFixNumber",cardInfo.MemberCardFixNumber),
                new SqlParameter("@MemberCardRegDate",cardInfo.MemberCardRegDate),
                new SqlParameter("@MemberCardStatus",cardInfo.MemberCardStatus),
            };

            return db.Database.ExecuteSqlCommand(insertSql, parmarter.ToArray());
        }

        public int UpdateMemberPhone(string phone, Guid memberKye)
        {
            var updateSql = "UPDATE MemberInfo SET MemberPhoneNumber = @MemberPhoneNumber WHERE MemberKey = @MemberKey";
            var parmarter = new List<SqlParameter>()
            {
                new SqlParameter("@MemberPhoneNumber",phone),
                new SqlParameter("@MemberKey",memberKye),
            };

            return db.Database.ExecuteSqlCommand(updateSql, parmarter.ToArray());
        }

        public int InsertRecord(MemberGiftRecord memberGift)
        {
            var insertSql = "INSERT INTO MemberGiftRecord(MemberKey,GiftTypen,OrderCode,CreateTime) VALUES(@MemberKey,@GiftTypen,@OrderCode,@CreateTime)";
            var parmarter = new List<SqlParameter>()
            {
                new SqlParameter("@MemberKey",memberGift.MemberKey),
                new SqlParameter("@GiftTypen",memberGift.GiftTypen),
                new SqlParameter("@OrderCode",memberGift.OrderCode),
                new SqlParameter("@CreateTime",memberGift.CreateTime),
            };

            return db.Database.ExecuteSqlCommand(insertSql, parmarter.ToArray());
        }

    }
}
